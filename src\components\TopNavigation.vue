<template>
  <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
    <div class="container-fluid">
      <!-- Logo -->
      <router-link to="/language" class="navbar-brand fw-bold gradient-text">
        📚考試練習系統
      </router-link>

      <!-- Mobile toggle -->
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- Navigation items -->
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-pen me-1"></i>
              測驗
            </a>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" @click.prevent="selectChinese">
                  <i class="fas fa-language me-2"></i>中文測驗
                </a></li>
              <li><a class="dropdown-item" href="#" @click.prevent="selectEnglish">
                  <i class="fas fa-globe me-2"></i>英文測驗
                </a></li>
            </ul>
          </li>

          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-book me-1"></i>
              題庫
            </a>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" @click.prevent="selectChineseQuestionBank">
                  <i class="fas fa-language me-2"></i>中文題庫
                </a></li>
              <li><a class="dropdown-item" href="#" @click.prevent="selectEnglishQuestionBank">
                  <i class="fas fa-globe me-2"></i>英文題庫
                </a></li>
            </ul>
          </li>
          <li class="nav-item">
            <router-link to="/history" class="nav-link" :class="{ active: $route.path.startsWith('/history') }">
              <i class="fas fa-history me-1"></i>
              考試紀錄
            </router-link>
          </li>
        </ul>

        <!-- User info -->
        <div class="navbar-nav" v-if="examStore.user.isLoggedIn">
          <div class="nav-item dropdown">
            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button"
              data-bs-toggle="dropdown">
              <i class="fas fa-user-circle me-2"></i>
              {{ getUserDisplayName }}
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li>
                <span class="dropdown-item-text">
                  <i class="fas fa-info-circle me-2"></i>
                  登入者：{{ getUserDisplayName }}
                </span>
              </li>
              <li>
                <hr class="dropdown-divider">
              </li>
              <li>
                <button class="dropdown-item text-danger" @click="handleLogout">
                  <i class="fas fa-sign-out-alt me-2"></i>
                  登出
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useExamStore } from '../stores/examStore'

const router = useRouter()
const examStore = useExamStore()

// 獲取用戶顯示名稱（電郵@前的部分）
const getUserDisplayName = computed(() => {
  if (examStore.user.email) {
    return examStore.user.email.split('@')[0]
  }
  return '用戶'
})

const selectChinese = () => {
  examStore.selectLanguage('中文')
  examStore.setMode('exam') // 設置為考試模式
  router.push('/chapters')
}

const selectEnglish = () => {
  examStore.selectLanguage('英文')
  examStore.setMode('exam') // 設置為考試模式
  router.push('/chapters')
}

const selectChineseQuestionBank = () => {
  examStore.selectLanguage('中文')
  examStore.setMode('questionBank') // 設置為題庫模式
  router.push('/chapters')
}

const selectEnglishQuestionBank = () => {
  examStore.selectLanguage('英文')
  examStore.setMode('questionBank') // 設置為題庫模式
  router.push('/chapters')
}

const handleLogout = async () => {
  await examStore.logout()
  router.push('/')
}
</script>

<style scoped>
.navbar-brand {
  font-size: 1.5rem;
}

.nav-link.active {
  color: #667eea !important;
  font-weight: 600;
}

.nav-link:hover {
  color: #667eea !important;
}

.dropdown-item-text {
  color: #6c757d;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .navbar-brand {
    font-size: 1.2rem;
  }
}
</style>
